{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}
{% load form_utils %}
{% load django_permissions %}

{% block title %}قائمة الموظفين - نظام الدولية{% endblock %}

{% block page_title %}إدارة الموظفين{% endblock %}
{% block page_subtitle %}عرض وإدارة بيانات الموظفين في النظام{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title">
            <i class="fas fa-users page-title-icon"></i>
            <span>إدارة الموظفين</span>
        </div>
        <div class="page-header-actions">
            {% if perms.Hr.add_employee or user|is_admin %}
            <a href="{% url 'Hr:employees:create' %}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i>
                <span>إضافة موظف جديد</span>
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stats-card">
        <div class="stats-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="stats-content">
            <div class="stats-number">{{ total_employees }}</div>
            <div class="stats-label">إجمالي الموظفين</div>
            <div class="stats-description">في النظام</div>
        </div>
    </div>

    <div class="stats-card stats-card-success">
        <div class="stats-icon">
            <i class="fas fa-user-check"></i>
        </div>
        <div class="stats-content">
            <div class="stats-number">{{ active_employees }}</div>
            <div class="stats-label">الموظفين النشطين</div>
            <div class="stats-description">{% widthratio active_employees total_employees 100 %}% من الإجمالي</div>
        </div>
    </div>

    <div class="stats-card stats-card-warning">
        <div class="stats-icon">
            <i class="fas fa-pause-circle"></i>
        </div>
        <div class="stats-content">
            <div class="stats-number">{{ on_leave_employees }}</div>
            <div class="stats-label">في إجازة</div>
            <div class="stats-description">موظفين مؤقتاً</div>
        </div>
    </div>

    <div class="stats-card stats-card-danger">
        <div class="stats-icon">
            <i class="fas fa-user-times"></i>
        </div>
        <div class="stats-content">
            <div class="stats-number">{{ resigned_employees }}</div>
            <div class="stats-label">المستقيلين</div>
            <div class="stats-description">موظفين سابقين</div>
        </div>
    </div>
</div>

<!-- Status Toggle -->
<div class="card">
    <div class="card-body">
        <div class="status-toggle-container">
            <div class="status-toggle-content">
                <div class="toggle-wrapper">
                    <input id="employeeStatusToggle" type="checkbox" class="toggle-input" {% if status != 'inactive' %}checked{% endif %}>
                    <label for="employeeStatusToggle" class="toggle-label">
                        <span class="toggle-slider">
                            <i class="fas fa-user-check toggle-icon-active"></i>
                            <i class="fas fa-user-times toggle-icon-inactive"></i>
                        </span>
                    </label>
                </div>
                <div class="toggle-text">
                    <h6 id="toggleStatusText" class="{% if status == 'inactive' %}text-danger{% else %}text-success{% endif %}">
                        <i id="toggleStatusIcon" class="fas {% if status == 'inactive' %}fa-user-times{% else %}fa-user-check{% endif %}"></i>
                        <span id="toggleStatusLabel">{% if status == 'inactive' %}موظفين غير نشطين{% else %}موظفين نشطين{% endif %}</span>
                    </h6>
                    <p class="text-muted">انقر للتبديل بين عرض الموظفين النشطين وغير النشطين</p>
                </div>
            </div>
            <div class="toggle-stats">
                <div class="mini-stats">
                    <div class="mini-stat">
                        <div class="mini-stat-icon text-success">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="mini-stat-number text-success" id="toggleActiveCount">{{ active_employees }}</div>
                        <div class="mini-stat-label">نشط</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon text-danger">
                            <i class="fas fa-user-times"></i>
                        </div>
                        <div class="mini-stat-number text-danger" id="toggleInactiveCount">{{ resigned_employees }}</div>
                        <div class="mini-stat-label">مستقيل</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Search and Filter Section -->
<div class="card">
    <div class="card-header">
        <div class="card-header-content">
            <div class="card-title">
                <i class="fas fa-filter"></i>
                <span>البحث والتصفية المتقدمة</span>
            </div>
            <div class="card-subtitle">ابحث عن الموظفين باستخدام معايير متعددة ومتقدمة</div>
        </div>
        <div class="card-header-actions">
            <span class="badge badge-secondary">{{ employees|length }} نتيجة</span>
        </div>
    </div>
    <div class="card-body">
        <form method="get" action="" id="employeeFilterForm">
            <!-- Active Filters -->
            {% if request.GET %}
            <div class="active-filters">
                <div class="active-filters-header">
                    <span class="active-filters-title">
                        <i class="fas fa-filter"></i> الفلاتر النشطة
                    </span>
                    <a href="{% url 'Hr:employees:list' %}" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-times-circle"></i> مسح الكل
                    </a>
                </div>
                <div class="filter-tags">
                    {% if request.GET.search %}
                    <span class="filter-tag">
                        الاسم: {{ request.GET.search }}
                        <a href="#" class="filter-tag-remove" data-field="search"><i class="fas fa-times"></i></a>
                    </span>
                    {% endif %}
                    {% if request.GET.emp_code %}
                    <span class="filter-tag">
                        الكود: {{ request.GET.emp_code }}
                        <a href="#" class="filter-tag-remove" data-field="emp_code"><i class="fas fa-times"></i></a>
                    </span>
                    {% endif %}
                    {% if request.GET.department %}
                    <span class="filter-tag">
                        القسم: {{ filter_form.department.field.choices|dictsort:"0"|dict_get:request.GET.department }}
                        <a href="#" class="filter-tag-remove" data-field="department"><i class="fas fa-times"></i></a>
                    </span>
                    {% endif %}
                    {% if request.GET.job %}
                    <span class="filter-tag">
                        الوظيفة
                        <a href="#" class="filter-tag-remove" data-field="job"><i class="fas fa-times"></i></a>
                    </span>
                    {% endif %}
                    {% if request.GET.working_condition and request.GET.working_condition != 'سارى' %}
                    <span class="filter-tag">
                        حالة العمل: {{ request.GET.working_condition }}
                        <a href="#" class="filter-tag-remove" data-field="working_condition"><i class="fas fa-times"></i></a>
                    </span>
                    {% endif %}
                    {% if request.GET.insurance_status %}
                    <span class="filter-tag">
                        حالة التأمين: {{ request.GET.insurance_status }}
                        <a href="#" class="filter-tag-remove" data-field="insurance_status"><i class="fas fa-times"></i></a>
                    </span>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Search Section -->
            <div class="search-section">
                <div class="search-row">
                    <div class="search-main">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" name="search" class="form-control"
                                   placeholder="بحث شامل: الاسم، الكود، الرقم القومي، الهاتف، العنوان..."
                                   value="{{ request.GET.search|default:'' }}" autocomplete="off">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>بحث
                            </button>
                        </div>
                    </div>
                    <div class="search-actions">
                        <button type="button" class="btn btn-outline-secondary" id="advancedSearchToggle">
                            <i class="fas fa-sliders-h"></i>فلاتر متقدمة
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="viewToggle" title="تبديل العرض">
                            <i class="fas fa-th-large"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filter Groups -->
            <div class="filter-groups">
                <!-- Basic Filters -->
                <div class="filter-group">
                    <div class="filter-group-header">
                        <h6 class="filter-group-title">
                            <i class="fas fa-filter"></i>
                            الفلاتر الأساسية
                        </h6>
                    </div>
                    <div class="filter-grid">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-id-badge"></i>
                                كود الموظف
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-hashtag"></i>
                                </span>
                                <input type="text" name="emp_code" class="form-control"
                                       placeholder="أدخل كود الموظف" value="{{ request.GET.emp_code|default:'' }}">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-building"></i>
                                {{ filter_form.department.label }}
                            </label>
                            {{ filter_form.department }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-briefcase"></i>
                                {{ filter_form.job.label }}
                            </label>
                            {{ filter_form.job }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-user-check"></i>
                                {{ filter_form.working_condition.label }}
                            </label>
                            {{ filter_form.working_condition }}
                        </div>
                    </div>
                </div>

                <!-- Advanced Filters (Collapsible) -->
                <div class="collapse" id="advancedFilters">
                    <div class="filter-group">
                        <div class="filter-group-header">
                            <h6 class="filter-group-title">
                                <i class="fas fa-search-plus"></i>
                                فلاتر متقدمة
                            </h6>
                        </div>
                        <div class="filter-grid">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-phone"></i>
                                    رقم الهاتف
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-phone"></i>
                                    </span>
                                    <input type="text" name="phone" class="form-control"
                                           placeholder="بحث برقم الهاتف" value="{{ request.GET.phone|default:'' }}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-id-card"></i>
                                    الرقم القومي
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-id-card"></i>
                                    </span>
                                    <input type="text" name="national_id" class="form-control"
                                           placeholder="بحث بالرقم القومي" value="{{ request.GET.national_id|default:'' }}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-shield-alt"></i>
                                    {{ filter_form.insurance_status.label }}
                                </label>
                                {{ filter_form.insurance_status }}
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-car"></i>
                                    السيارة
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-car"></i>
                                    </span>
                                    <input type="text" name="car" class="form-control"
                                           placeholder="بحث بالسيارة" value="{{ request.GET.car|default:'' }}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-map-marker-alt"></i>
                                    نقطة التقاط السيارة
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </span>
                                    <input type="text" name="car_pick_up_point" class="form-control"
                                           placeholder="نقطة التقاط السيارة" value="{{ request.GET.car_pick_up_point|default:'' }}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-clock"></i>
                                    نوع الوردية
                                </label>
                                <select name="shift_type" class="form-select">
                                    <option value="">جميع الورديات</option>
                                    <option value="صباحي" {% if request.GET.shift_type == 'صباحي' %}selected{% endif %}>صباحي</option>
                                    <option value="مسائي" {% if request.GET.shift_type == 'مسائي' %}selected{% endif %}>مسائي</option>
                                    <option value="ليلي" {% if request.GET.shift_type == 'ليلي' %}selected{% endif %}>ليلي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="filter-actions">
                <div class="filter-actions-row">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        تطبيق الفلاتر
                    </button>
                    <a href="{% url 'Hr:employees:list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </a>
                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="collapse"
                            data-bs-target="#advancedFilters" aria-expanded="false" aria-controls="advancedFilters">
                        <i class="fas fa-sliders-h"></i>
                        متقدم
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Employee List Container -->
<div class="card">
    <div class="card-header">
        <div class="card-header-content">
            <div class="card-title" id="employeeListTitle">
                {% if status == 'inactive' %}
                <i id="employeeListIcon" class="fas fa-user-times text-danger"></i>
                <span id="employeeListTitleText">قائمة الموظفين غير النشطين</span>
                {% else %}
                <i id="employeeListIcon" class="fas fa-users text-success"></i>
                <span id="employeeListTitleText">قائمة الموظفين النشطين</span>
                {% endif %}
            </div>
            {% if employees %}
            <div class="card-subtitle">
                <span class="badge badge-primary" id="employeeListCount">{{ employees|length }}</span> موظف
                {% if request.GET %}
                <span class="text-muted">
                    <i class="fas fa-filter"></i>
                    مطابق لمعايير البحث
                </span>
                {% endif %}
            </div>
            {% endif %}
        </div>
        <div class="card-header-actions">
            <!-- View Toggle -->
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary active" id="tableViewBtn" title="عرض جدول">
                    <i class="fas fa-table"></i>
                </button>
                <button type="button" class="btn btn-outline-secondary" id="cardViewBtn" title="عرض بطاقات">
                    <i class="fas fa-th-large"></i>
                </button>
            </div>

            <!-- Sort Dropdown -->
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-sort"></i>
                    ترتيب
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                    <li><h6 class="dropdown-header">ترتيب حسب</h6></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="emp_id">
                        <i class="fas fa-hashtag"></i>رقم الموظف
                    </a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="emp_full_name">
                        <i class="fas fa-user"></i>الاسم
                    </a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="department">
                        <i class="fas fa-building"></i>القسم
                    </a></li>
                    <li><a class="dropdown-item sort-option" href="#" data-sort="emp_date_hiring">
                        <i class="fas fa-calendar"></i>تاريخ التعيين
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><h6 class="dropdown-header">اتجاه الترتيب</h6></li>
                    <li><a class="dropdown-item sort-direction" href="#" data-direction="asc">
                        <i class="fas fa-arrow-up"></i>تصاعدي
                    </a></li>
                    <li><a class="dropdown-item sort-direction" href="#" data-direction="desc">
                        <i class="fas fa-arrow-down"></i>تنازلي
                    </a></li>
                </ul>
            </div>

            <!-- Add Employee Button -->
            {% if perms.Hr.add_employee or user|is_admin %}
            <a href="{% url 'Hr:employees:create' %}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i>
                إضافة موظف جديد
            </a>
            {% endif %}
        </div>
    </div>
    <div class="card-body" id="employeeListContainer">
        {% if employees %}
        <!-- Card View (Hidden by default) -->
        <div id="cardView" class="employee-cards-container" style="display: none;">
            <div class="cards-grid">
                {% for employee in employees %}
                <div class="employee-card" data-emp-id="{{ employee.emp_id }}"
                     data-emp-name="{{ employee.emp_full_name|default:employee.emp_first_name }}"
                     data-dept="{{ employee.department.dept_name|default:'' }}"
                     data-condition="{{ employee.working_condition|default:'' }}">
                    <div class="card">
                        <div class="card-body">
                            <!-- Employee Header -->
                            <div class="employee-header">
                                <div class="employee-avatar">
                                    {% if employee.emp_image %}
                                    <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}"
                                         class="avatar">
                                    {% else %}
                                    <div class="avatar avatar-placeholder">
                                        {{ employee.emp_first_name|slice:":1"|upper }}
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="employee-info">
                                    <h6 class="employee-name">
                                        {{ employee.emp_full_name|default:employee.emp_first_name }}
                                    </h6>
                                    <div class="employee-id">
                                        <span class="badge badge-secondary">
                                            <i class="fas fa-hashtag"></i>{{ employee.emp_id }}
                                        </span>
                                    </div>
                                </div>
                                <div class="employee-status">
                                    {% if employee.working_condition == 'سارى' %}
                                    <span class="status-indicator status-success"></span>
                                    {% elif employee.working_condition == 'منقطع عن العمل' %}
                                    <span class="status-indicator status-warning"></span>
                                    {% elif employee.working_condition == 'استقالة' %}
                                    <span class="status-indicator status-danger"></span>
                                    {% else %}
                                    <span class="status-indicator status-secondary"></span>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Employee Details -->
                            <div class="employee-details">
                                <div class="detail-row">
                                    <i class="fas fa-building"></i>
                                    <span class="detail-label">القسم:</span>
                                    <span class="detail-value">
                                        {{ employee.department.dept_name|default:"-" }}
                                    </span>
                                </div>
                                <div class="detail-row">
                                    <i class="fas fa-briefcase"></i>
                                    <span class="detail-label">الوظيفة:</span>
                                    <span class="detail-value">
                                        {{ employee.jop_name|default:"-" }}
                                    </span>
                                </div>
                                {% if employee.emp_phone1 %}
                                <div class="detail-row">
                                    <i class="fas fa-phone"></i>
                                    <span class="detail-label">الهاتف:</span>
                                    <a href="tel:{{ employee.emp_phone1 }}" class="detail-value detail-link">
                                        {{ employee.emp_phone1 }}
                                    </a>
                                </div>
                                {% endif %}
                                {% if employee.national_id %}
                                <div class="detail-row">
                                    <i class="fas fa-id-card"></i>
                                    <span class="detail-label">الرقم القومي:</span>
                                    <span class="detail-value">
                                        {{ employee.national_id }}
                                    </span>
                                </div>
                                {% endif %}
                            </div>

                            <!-- Working Status Badge -->
                            <div class="employee-status-badge">
                                {% if employee.working_condition == 'سارى' %}
                                <span class="badge badge-success badge-full">
                                    <i class="fas fa-check-circle"></i>نشط
                                </span>
                                {% elif employee.working_condition == 'منقطع عن العمل' %}
                                <span class="badge badge-warning badge-full">
                                    <i class="fas fa-pause-circle"></i>منقطع عن العمل
                                </span>
                                {% elif employee.working_condition == 'استقالة' %}
                                <span class="badge badge-danger badge-full">
                                    <i class="fas fa-times-circle"></i>استقالة
                                </span>
                                {% else %}
                                <span class="badge badge-secondary badge-full">
                                    <i class="fas fa-question-circle"></i>{{ employee.working_condition|default:"-" }}
                                </span>
                                {% endif %}
                            </div>

                            <!-- Action Buttons -->
                            <div class="employee-actions">
                                <div class="btn-group" role="group">
                                    <a href="{% url 'Hr:employees:detail' employee.emp_id %}"
                                       class="btn btn-outline-primary btn-sm" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>عرض
                                    </a>
                                    {% if perms.Hr.change_employee or user|is_admin %}
                                    <a href="{% url 'Hr:employees:edit' employee.emp_id %}"
                                       class="btn btn-primary btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>تعديل
                                    </a>
                                    {% endif %}
                                    <div class="dropdown">
                                        <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle"
                                                data-bs-toggle="dropdown" aria-expanded="false" title="المزيد">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" href="{% url 'Hr:employees:detail' employee.emp_id %}">
                                                <i class="fas fa-id-card"></i>البطاقة الشخصية
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-print"></i>طباعة البيانات
                                            </a></li>
                                            {% if perms.Hr.delete_employee or user|is_admin %}
                                            <li><hr class="dropdown-divider"></li>
                                            <li><button class="dropdown-item text-danger delete-employee"
                                                        data-employee-id="{{ employee.emp_id }}"
                                                        data-employee-name="{{ employee.emp_full_name }}">
                                                <i class="fas fa-trash"></i>حذف
                                            </button></li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Table View -->
        <div id="tableView" class="table-responsive">
            <table class="table" id="employeesTable">
                <thead>
                    <tr>
                        <th class="sortable" data-sort="emp_id">
                            <div class="table-header">
                                <i class="fas fa-hashtag"></i>
                                الرقم
                                <i class="fas fa-sort"></i>
                            </div>
                        </th>
                        <th class="sortable" data-sort="emp_full_name">
                            <div class="table-header">
                                <i class="fas fa-user"></i>
                                الموظف
                                <i class="fas fa-sort"></i>
                            </div>
                        </th>
                        <th class="sortable" data-sort="department">
                            <div class="table-header">
                                <i class="fas fa-building"></i>
                                القسم
                                <i class="fas fa-sort"></i>
                            </div>
                        </th>
                        <th>
                            <div class="table-header">
                                <i class="fas fa-briefcase"></i>
                                الوظيفة
                            </div>
                        </th>
                        <th>
                            <div class="table-header">
                                <i class="fas fa-phone"></i>
                                الهاتف
                            </div>
                        </th>
                        <th class="sortable" data-sort="working_condition">
                            <div class="table-header">
                                <i class="fas fa-user-check"></i>
                                الحالة
                                <i class="fas fa-sort"></i>
                            </div>
                        </th>
                        <th>
                            <div class="table-header">
                                <i class="fas fa-cogs"></i>
                                العمليات
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr class="employee-row" data-emp-id="{{ employee.emp_id }}"
                        data-emp-name="{{ employee.emp_full_name|default:employee.emp_first_name }}"
                        data-dept="{{ employee.department.dept_name|default:'' }}"
                        data-condition="{{ employee.working_condition|default:'' }}">

                        <!-- Employee ID -->
                        <td>
                            <span class="badge badge-primary">
                                #{{ employee.emp_id }}
                            </span>
                        </td>

                        <!-- Employee Info -->
                        <td>
                            <div class="employee-info-cell">
                                <div class="employee-avatar">
                                    {% if employee.emp_image %}
                                    <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}"
                                         class="avatar">
                                    {% else %}
                                    <div class="avatar avatar-placeholder">
                                        {{ employee.emp_first_name|slice:":1"|upper }}
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="employee-details">
                                    <div class="employee-name-wrapper">
                                        <a href="{% url 'Hr:employees:detail' employee.emp_id %}"
                                           class="employee-name">
                                            {{ employee.emp_full_name|default:employee.emp_first_name }}
                                        </a>
                                    </div>
                                    {% if employee.national_id %}
                                    <div class="employee-meta">
                                        <small class="text-muted">
                                            <i class="fas fa-id-card"></i>
                                            {{ employee.national_id }}
                                        </small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>

                        <!-- Department -->
                        <td>
                            {% if employee.department %}
                            <div class="department-info">
                                <span class="badge badge-info">
                                    <i class="fas fa-building"></i>
                                    {{ employee.department.dept_name }}
                                </span>
                            </div>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>

                        <!-- Job -->
                        <td>
                            {% if employee.jop_name %}
                            <div class="job-info">
                                <span class="job-title">{{ employee.jop_name }}</span>
                            </div>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>

                        <!-- Phone -->
                        <td>
                            {% if employee.emp_phone1 %}
                            <a href="tel:{{ employee.emp_phone1 }}" class="phone-link">
                                <i class="fas fa-phone"></i>
                                <span>{{ employee.emp_phone1 }}</span>
                            </a>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <!-- Status -->
                        <td>
                            <div class="status-wrapper">
                                {% if employee.working_condition == 'سارى' %}
                                <span class="badge badge-success">
                                    <i class="fas fa-check-circle"></i>نشط
                                </span>
                                {% elif employee.working_condition == 'منقطع عن العمل' %}
                                <span class="badge badge-warning">
                                    <i class="fas fa-pause-circle"></i>منقطع
                                </span>
                                {% elif employee.working_condition == 'استقالة' %}
                                <span class="badge badge-danger">
                                    <i class="fas fa-times-circle"></i>استقالة
                                </span>
                                {% else %}
                                <span class="badge badge-secondary">
                                    <i class="fas fa-question-circle"></i>{{ employee.working_condition|default:"غير محدد" }}
                                </span>
                                {% endif %}
                            </div>
                        </td>

                        <!-- Actions -->
                        <td>
                            <div class="action-buttons">
                                <div class="btn-group" role="group">
                                    <a href="{% url 'Hr:employees:detail' employee.emp_id %}"
                                       class="btn btn-outline-primary btn-sm" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if perms.Hr.change_employee or user|is_admin %}
                                    <a href="{% url 'Hr:employees:edit' employee.emp_id %}"
                                       class="btn btn-primary btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    <div class="dropdown">
                                        <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle"
                                                data-bs-toggle="dropdown" aria-expanded="false" title="المزيد">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><h6 class="dropdown-header">إجراءات الموظف</h6></li>
                                            <li><a class="dropdown-item" href="{% url 'Hr:employees:detail' employee.emp_id %}">
                                                <i class="fas fa-id-card"></i>البطاقة الشخصية
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-print"></i>طباعة البيانات
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-file-export"></i>تصدير البيانات
                                            </a></li>
                                            {% if perms.Hr.delete_employee or user|is_admin %}
                                            <li><hr class="dropdown-divider"></li>
                                            <li><button class="dropdown-item text-danger delete-employee"
                                                        data-employee-id="{{ employee.emp_id }}"
                                                        data-employee-name="{{ employee.emp_full_name }}">
                                                <i class="fas fa-trash"></i>حذف الموظف
                                            </button></li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% else %}
        <div class="empty-state">
            <i class="fas fa-users"></i>
            <p>لا يوجد موظفين بالمعايير المحددة.</p>
        </div>
        {% endif %}
            </div>
        </div>

    {% if employees_by_department %}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">
                <i class="fas fa-chart-pie"></i>
                توزيع الموظفين حسب الأقسام
            </h5>
        </div>
        <div class="card-body">
            <div class="stats-grid">
                {% for dept in employees_by_department %}
                <div class="stat-item">
                    <div class="stat-card">
                         <h6 class="stat-title">{{ dept.dept_name }}</h6>
                         <div class="stat-value-row">
                             <span class="stat-value">{{ dept.count }}</span>
                             {% if dept.dept_code %}
                             <a href="{% url 'Hr:departments:detail' dept.dept_code %}" class="btn btn-sm btn-link">عرض</a>
                             {% endif %}
                         </div>
                         <div class="progress-bar-container">
                             <div class="progress-bar"
                                  style="width: {% widthratio dept.count total_employees 100 %}%;">
                             </div>
                         </div>
                         <small class="stat-percentage">{% widthratio dept.count total_employees 100 %}% من الإجمالي</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">روابط سريعة</h5>
            </div>
            <div class="card-body">
                <div class="quick-links-grid">
                    <div class="quick-link-item">
                        <a href="{% url 'Hr:employees:list' %}" class="quick-link-card">
                            <div class="quick-link-content">
                                <div class="quick-link-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="quick-link-text">
                                    <h6>قائمة الموظفين</h6>
                                    <p>عرض وإدارة بيانات الموظفين</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="quick-link-item">
                        <a href="{% url 'Hr:employees:create' %}" class="quick-link-card">
                            <div class="quick-link-content">
                                <div class="quick-link-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="quick-link-text">
                                    <h6>إضافة موظف جديد</h6>
                                    <p>تسجيل بيانات موظف جديد</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="quick-link-item">
                        <a href="{% url 'Hr:departments:list' %}" class="quick-link-card">
                            <div class="quick-link-content">
                                <div class="quick-link-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="quick-link-text">
                                    <h6>الأقسام</h6>
                                    <p>عرض وإدارة الأقسام</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="quick-link-item">
                        <a href="{% url 'Hr:jobs:list' %}" class="quick-link-card">
                            <div class="quick-link-content">
                                <div class="quick-link-icon">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <div class="quick-link-text">
                                    <h6>الوظائف</h6>
                                    <p>عرض وإدارة المسميات الوظيفية</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notifications -->
<div class="toast-container">
    <div id="successToast" class="toast toast-success" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-content">
            <div class="toast-body">
                <i class="fas fa-check-circle"></i>
                <span class="toast-message"></span>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

<!-- Loading Indicator -->
<div id="loadingIndicator" class="loading-indicator" style="display: none;">
    <div class="progress-bar">
        <div class="progress-bar-fill"></div>
    </div>
</div>

<!-- Quick Actions Button -->
<div class="quick-actions-fab">
    <button id="quickActionsBtn" type="button" class="fab-button">
        <i class="fas fa-bolt"></i>
    </button>
</div>

<!-- Quick Actions Menu -->
<div class="quick-actions-menu" style="display: none;">
    <h6 class="menu-title">إجراءات سريعة</h6>
    <div class="menu-items">
        <a href="{% url 'Hr:employees:create' %}" class="menu-item">
            <i class="fas fa-user-plus"></i>إضافة موظف جديد
        </a>
        <button class="menu-item" onclick="exportToExcel()">
            <i class="fas fa-file-excel"></i>تصدير إلى Excel
        </button>
        <button class="menu-item" onclick="printEmployeeList()">
            <i class="fas fa-print"></i>طباعة القائمة
        </button>
    </div>
</div>

<!-- Keyboard Shortcuts Guide -->
<div class="modal" id="keyboardShortcutsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-keyboard"></i>
                    اختصارات لوحة المفاتيح
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="shortcuts-grid">
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Ctrl</kbd> + <kbd>F</kbd>
                        </div>
                        <span class="shortcut-description">بحث سريع</span>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Ctrl</kbd> + <kbd>N</kbd>
                        </div>
                        <span class="shortcut-description">موظف جديد</span>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Ctrl</kbd> + <kbd>P</kbd>
                        </div>
                        <span class="shortcut-description">طباعة القائمة</span>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Ctrl</kbd> + <kbd>E</kbd>
                        </div>
                        <span class="shortcut-description">تصدير البيانات</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
