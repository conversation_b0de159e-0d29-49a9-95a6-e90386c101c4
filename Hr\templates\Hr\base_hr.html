{% load static %}
{% load i18n %}
{% load image_utils %}
<!DOCTYPE html>
<html lang="{{ current_language|default:'ar' }}" dir="{{ text_direction|default:'rtl' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="HR Management System - Comprehensive employee management solution">
    <title>{% block title %}نظام إدارة الموارد البشرية{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">

    <!-- Google Fonts (وراثة الخط من :root فقط) -->
    {% if system_settings.font_family == 'cairo' or current_font == 'Cairo' %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'tajawal' or current_font == 'Tajawal' %}
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'almarai' or current_font == 'Almarai' %}
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@400;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'ibm-plex-sans-arabic' or current_font == 'IBM Plex Sans Arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'noto-sans-arabic' or current_font == 'Noto Sans Arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% else %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    {% endif %}
    <!-- ربط ملف CSS الحديث -->
    <link rel="stylesheet" href="{% static 'Hr/css/hr_clean.css' %}">

    <!-- سيتم ربط ملف CSS الجديد هنا لاحقًا -->
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar d-flex justify-content-between align-items-center">
        <div class="fw-bold fs-4 text-primary">نظام الموارد البشرية</div>
        <div class="d-flex align-items-center gap-3">
            <span class="fw-bold">{{ request.user.get_full_name|default:'مستخدم' }}</span>
            <button id="themeToggle" class="btn btn-accent" title="تبديل الوضع الليلي/النهاري"><i id="themeIcon" class="fas fa-moon"></i></button>
            <a href="{% url 'accounts:logout' %}" class="btn btn-danger">تسجيل الخروج</a>
        </div>
    </nav>
    <div class="d-flex" style="min-height: 100vh;">
        <aside class="sidebar">
            <h4 class="mb-4 text-primary">الموارد البشرية</h4>
            <ul style="list-style:none; padding:0;">
                <li><a href="{% url 'Hr:dashboard' %}" class="d-block py-2 text-primary">لوحة التحكم</a></li>
                <li><a href="{% url 'Hr:employees:list' %}" class="d-block py-2">قائمة الموظفين</a></li>
                <li><a href="{% url 'Hr:employees:create' %}" class="d-block py-2">إضافة موظف</a></li>
                <li><a href="{% url 'Hr:departments:list' %}" class="d-block py-2">الأقسام</a></li>
                <li><a href="{% url 'Hr:jobs:list' %}" class="d-block py-2">الوظائف</a></li>
                <li><a href="{% url 'attendance:dashboard' %}" class="d-block py-2">الحضور والانصراف</a></li>
            </ul>
        </aside>
        <main class="flex-grow-1 p-4">
            {% block content %}{% endblock %}
        </main>
    </div>
    <script>
    function setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        document.getElementById('themeIcon').className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
    function getPreferredTheme() {
        const stored = localStorage.getItem('theme');
        if (stored) return stored;
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    document.addEventListener('DOMContentLoaded', function() {
        setTheme(getPreferredTheme());
        document.getElementById('themeToggle').addEventListener('click', function() {
            const current = document.documentElement.getAttribute('data-theme') || 'light';
            setTheme(current === 'dark' ? 'light' : 'dark');
        });
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
            if (!localStorage.getItem('theme')) {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });
    });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
